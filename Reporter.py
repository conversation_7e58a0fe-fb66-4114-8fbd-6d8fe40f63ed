# Responsible Disclosure
import pygame as pg
import sys
import os


pg.init()

# --- Constants ---
WIDTH, HEIGHT = 900, 600
screen = pg.display.set_mode((WIDTH, HEIGHT))
pg.display.set_caption("Responsible Disclosure")

font = pg.font.Font(os.path.join("fonts", "arcade.ttf"), 18)
report_img = pg.transform.scale(pg.image.load(os.path.join("img", "report.png")), (160, 160))
hacker_img = pg.transform.scale(pg.image.load(os.path.join("img", "hacker.png")), (100, 100))

report_rect = pg.Rect(WIDTH // 2 - 80, HEIGHT // 2 - 130, 160, 160)
report_btn_rect = pg.Rect(WIDTH // 2 - 100, HEIGHT // 2 + 40, 200, 40)
buy_rect = pg.Rect(WIDTH // 2 - 100, HEIGHT // 2 + 100, 200, 50)

bugs = 100_000_000_000
ai_reporters = 0
clock = pg.time.Clock()
ticks = 0
target_score = 100_000_000


class Fake:
    def __init__(self):
        self.value = [ord(c) for c in "totally_not_the_flag"]
    def confuse(self):
        return ''.join([chr((x ^ 42) + 3) for x in self.value])
FAKE_FLAG = Fake().confuse()

#password check
SALT = [56, 98, 99, 115, 101, 100]

def check_password(pw):
    scramble = ''.join([chr(ord(x)+2) for x in '7absec'])
    return pw == ''.join([chr(ord(c)-1) for c in scramble])


enc = [108, 102, 111, 103, 113, 81, 38, 109, 117, 46,
       104, 43, 125, 86, 117, 110, 94, 103, 155, 152, 89,
       107, 125, 168, 105, 110, 117, 95, 64, 106, 126, 181]

def decrypt_flag(token):
    return ''.join([chr((val ^ (token + i)) - i) for i, val in enumerate(enc)])

def get_password_screen():
    input_box = pg.Rect(150, 220, 400, 40)
    color_inactive = pg.Color('lightskyblue3')
    color_active = pg.Color('dodgerblue2')
    color = color_inactive
    active = False
    text = ''
    done = False
    error = False

    while not done:
        for event in pg.event.get():
            if event.type == pg.QUIT:
                pg.quit(); sys.exit()
            if event.type == pg.MOUSEBUTTONDOWN:
                active = input_box.collidepoint(event.pos)
                color = color_active if active else color_inactive
            if event.type == pg.KEYDOWN and active:
                if event.key == pg.K_RETURN:
                    if check_password(text):
                        done = True
                    else:
                        error = True
                        text = ''
                elif event.key == pg.K_BACKSPACE:
                    text = text[:-1]
                else:
                    if len(text) < 20:
                        text += event.unicode

        screen.fill((20, 20, 20))
        screen.blit(font.render("Enter dev password to activate bounty engine:", True, (255, 255, 255)), (60, 150))
        txt_surface = font.render(text, True, (0, 255, 0))
        input_box.w = max(400, txt_surface.get_width()+10)
        screen.blit(txt_surface, (input_box.x+5, input_box.y+5))
        pg.draw.rect(screen, color, input_box, 2)
        if error:
            screen.blit(font.render("Access Denied!", True, (255, 0, 0)), (250, 280))
        pg.display.flip()
        clock.tick(30)

# def show_victory():
#     tokentemp = 100_00_00
#     while bugs >= target_score:
#         tokentemp -= 2**20
#         token = 10 * 10**8
#         flag = decrypt_flag(token/10**8)

def show_victory():
    global bugs
    tokentemp = 100_000_000
    token = 0
    while tokentemp >= (target_score + 2**20):
        tokentemp -= 2**20
    token = tokentemp // 10**7  #
    flag = decrypt_flag(token)
    screen.fill((0, 0, 0))
    screen.blit(font.render("Elite Bug Hunter!", True, (0, 255, 0)), (180, 200))
    screen.blit(font.render(f"Flag: {flag}", True, (255, 255, 0)), (100, 250))
    pg.display.flip()
    pg.time.wait(10000)
    pg.quit()
    sys.exit()

def draw():
    screen.fill((30, 30, 30))
    screen.blit(hacker_img, (WIDTH - 120, 20))
    screen.blit(font.render("RESPONSIBLE DISCLOSURE", True, (0, 255, 255)), (30, 30))

    stat1 = font.render(f"BUGS REPORTED:  {bugs}", True, (255, 255, 0))
    stat2 = font.render(f"AI-REPORTERS: {ai_reporters}", True, (255, 255, 255))
    screen.blit(stat1, (WIDTH // 2 - stat1.get_width() // 2, 100))
    screen.blit(stat2, (WIDTH // 2 - stat2.get_width() // 2, 130))

    screen.blit(report_img, report_rect.topleft)
    pg.draw.rect(screen, (70, 70, 200), report_btn_rect, border_radius=6)
    rpt_text = font.render("REPORT BUG", True, (255, 255, 255))
    screen.blit(rpt_text, (report_btn_rect.x + (report_btn_rect.width - rpt_text.get_width()) // 2, report_btn_rect.y + 10))

    pg.draw.rect(screen, (20, 200, 50), buy_rect, border_radius=6)
    buy_text = font.render("BUY AI REPORTER", True, (255, 255, 255))
    screen.blit(buy_text, (buy_rect.x + (buy_rect.width - buy_text.get_width()) // 2, buy_rect.y + 15))

    pg.display.flip()

# Launch game
get_password_screen()

while True:
    for event in pg.event.get():
        if event.type == pg.QUIT:
            pg.quit(); sys.exit()
        elif event.type == pg.MOUSEBUTTONDOWN:
            if report_btn_rect.collidepoint(event.pos):
                bugs += 1
            elif buy_rect.collidepoint(event.pos):
                if bugs >= 100:
                    bugs -= 100
                    ai_reporters += 1

    if ticks % 60 == 0:
        bugs += ai_reporters

    if bugs >= target_score:
        show_victory()

    draw()
    ticks += 1
    clock.tick(60)